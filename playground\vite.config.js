import path from "path";

import { defineConfig } from "vite";
import { mockDevServerPlugin } from "vite-plugin-mock-dev-server";

import { dependencyInfoPlugin } from "@cscs-agent/presets/vite/vite-dependency-info-plugin.js";
import typescript from "@rollup/plugin-typescript";
import tailwindcss from "@tailwindcss/vite";
import react from "@vitejs/plugin-react";

// https://vite.dev/config/
export default defineConfig({
  base: "/",
  plugins: [
    mockDevServerPlugin(),
    react(),
    typescript(),
    tailwindcss(),
    dependencyInfoPlugin({
      includeDevDependencies: true, // Include dev dependencies in development
      includeWorkspaceDependencies: true, // Include workspace dependencies
      outputMethod: "both", // Output to both console and global variable
      globalVariableName: "__DEPENDENCY_INFO__",
      outputOnStartup: true,
    }),
  ], // Add TypeScript plugin
  server: {
    port: 3000,
    proxy: {
      "^/api": {
        // target: "http://************:5800",
        target: "http://localhost:8000",
        changeOrigin: true,
      },
      "/cluster-api": {
        target: "http://************:8002",
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/cluster-api/, "/api"),
      },
    },
  },
  // Add TypeScript configuration
  build: {
    sourcemap: false,
  },
  resolve: {
    extensions: [".js", ".ts", ".jsx", ".tsx", ".json"],
    alias: {
      "@": path.resolve(__dirname, "src"),
      "@@": path.resolve(__dirname, "node_modules"),
    },
  },
  esbuild: {
    loader: "tsx",
    include: /\.(ts|tsx|jsx)$/,
  },
  css: {
    preprocessorOptions: {
      less: {
        javascriptEnabled: true,
      },
    },
  },
});
