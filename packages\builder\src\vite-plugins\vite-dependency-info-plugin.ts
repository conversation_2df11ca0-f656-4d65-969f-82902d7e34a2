import fs from "fs";
import path from "path";

/**
 * Vite plugin that collects dependency version information from package.json
 * and makes it available at runtime
 */
export function appDepInfoPlugin(options = {}) {
  const {
    // Whether to include devDependencies in the output (default: false for production builds)
    includeDevDependencies = false,
    // Whether to include workspace dependencies (default: true)
    includeWorkspaceDependencies = true,
    // Custom filter function to determine which dependencies to include
    filter = null,
    // Output method: 'console' | 'global' | 'both' (default: 'console')
    outputMethod = "console",
    // Global variable name when using 'global' or 'both' output methods
    globalVariableName = "__DEPENDENCY_INFO__",
    // Whether to output on app startup (default: true)
    outputOnStartup = true,
    // Custom package.json path (relative to project root)
    packageJsonPath = "package.json",
  } = options;

  let dependencyInfo = null;
  let isProduction = false;

  return {
    name: "dependency-info",

    configResolved(config) {
      isProduction = config.command === "build";
    },

    buildStart() {
      try {
        // Read package.json from the project root
        const packageJsonFullPath = path.resolve(process.cwd(), packageJsonPath);

        if (!fs.existsSync(packageJsonFullPath)) {
          this.warn(`package.json not found at ${packageJsonFullPath}`);
          return;
        }

        const packageJsonContent = fs.readFileSync(packageJsonFullPath, "utf-8");
        const packageJson = JSON.parse(packageJsonContent);

        // Collect dependency information
        const dependencies = packageJson.dependencies || {};
        const devDependencies = packageJson.devDependencies || {};

        // Determine which dependencies to include
        let allDependencies = { ...dependencies };

        // Include devDependencies based on options and build mode
        if (includeDevDependencies || (!isProduction && includeDevDependencies !== false)) {
          allDependencies = { ...allDependencies, ...devDependencies };
        }

        // Apply custom filter if provided
        if (typeof filter === "function") {
          const filteredDeps = {};
          Object.entries(allDependencies).forEach(([name, version]) => {
            if (filter(name, version, { isDevDependency: name in devDependencies })) {
              filteredDeps[name] = version;
            }
          });
          allDependencies = filteredDeps;
        }

        // Filter out workspace dependencies if not wanted
        if (!includeWorkspaceDependencies) {
          Object.keys(allDependencies).forEach((name) => {
            if (allDependencies[name].startsWith("workspace:")) {
              delete allDependencies[name];
            }
          });
        }

        dependencyInfo = {
          projectName: packageJson.name || "unknown",
          projectVersion: packageJson.version || "unknown",
          buildTime: new Date().toISOString(),
          isProduction,
          dependencies: allDependencies,
          totalDependencies: Object.keys(allDependencies).length,
        };

        this.info(`Collected ${dependencyInfo.totalDependencies} dependencies for runtime output`);
      } catch (error) {
        this.error(`Failed to read or parse package.json: ${error.message}`);
      }
    },

    generateBundle() {
      if (!dependencyInfo) {
        this.warn("No dependency information available to inject");
        return;
      }

      // Generate the runtime code that will be injected
      const runtimeCode = generateRuntimeCode(dependencyInfo, {
        outputMethod,
        globalVariableName,
        outputOnStartup,
      });

      // Emit a virtual module that can be imported
      this.emitFile({
        type: "asset",
        fileName: "dependency-info.js",
        source: runtimeCode,
      });
    },
  };
}

/**
 * Generate runtime code for dependency information output
 */
function generateRuntimeCode(dependencyInfo, options) {
  const { outputMethod, globalVariableName, outputOnStartup } = options;

  const dependencyInfoJson = JSON.stringify(dependencyInfo, null, 2);

  let code = `
// Dependency Information - Generated by vite-dependency-info-plugin
(function() {
  const dependencyInfo = ${dependencyInfoJson};
  
  function formatDependencyOutput(info) {
    const lines = [
      '🔧 Dependency Information',
      '========================',
      \`Project: \${info.projectName} v\${info.projectVersion}\`,
      \`Build Time: \${info.buildTime}\`,
      \`Environment: \${info.isProduction ? 'Production' : 'Development'}\`,
      \`Total Dependencies: \${info.totalDependencies}\`,
      '',
      'Dependencies:'
    ];
    
    Object.entries(info.dependencies)
      .sort(([a], [b]) => a.localeCompare(b))
      .forEach(([name, version]) => {
        lines.push(\`  \${name}: \${version}\`);
      });
    
    return lines.join('\\n');
  }
`;

  if (outputMethod === "console" || outputMethod === "both") {
    code += `
  // Console output
  if (typeof console !== 'undefined' && console.log) {
    const output = formatDependencyOutput(dependencyInfo);
    console.log(output);
  }
`;
  }

  if (outputMethod === "global" || outputMethod === "both") {
    code += `
  // Global variable assignment
  if (typeof window !== 'undefined') {
    window.${globalVariableName} = dependencyInfo;
  } else if (typeof global !== 'undefined') {
    global.${globalVariableName} = dependencyInfo;
  }
`;
  }

  if (outputOnStartup) {
    code += `
  // Auto-execute on startup
  if (typeof document !== 'undefined') {
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', function() {
        // Code already executed above
      });
    }
  }
`;
  }

  code += `
})();
`;

  return code;
}

export default dependencyInfoPlugin;
